import {
  createBrowserRouter,
  createRoutesFromElements,
  Route,
  Navigate,
} from "react-router-dom";
import { AuthRoutes } from "../features/auth/routes";
import { ProtectedRoutes } from "./protected-routes";

export const router = createBrowserRouter(
  createRoutesFromElements(
    <Route>
      <Route index element={<Navigate to="/dashboard" replace />} />
      <Route path="/auth/*" element={<AuthRoutes />} />
      <Route path="/dashboard/*" element={<ProtectedRoutes />} />
    </Route>
  )
);
