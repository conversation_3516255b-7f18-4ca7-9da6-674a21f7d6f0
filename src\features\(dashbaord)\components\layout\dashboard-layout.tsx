import { Outlet } from "react-router-dom";
import { useState } from "react";
import TopNavbar from "./top-navbar";
import SideNavbar from "./side-navbar";

function DashboardPage() {
  const [navbar, setNavbar] = useState(false);

  const handleNavbar = () => {
    setNavbar(!navbar);
  };

  return (
    <div className="w-full h-screen flex bg-[#F7F7F7]">
      <div className="w-full">
        {/* Top Navbar */}
        <TopNavbar handleNavbar={handleNavbar} navbar={navbar} />

        {/* Side navbar */}
        <div className="h-[calc(100vh-70px)] flex">
          <SideNavbar navbar={navbar} />
          <div className="flex-1 overflow-y-scroll p-6">
            <Outlet />
          </div>
        </div>
      </div>
    </div>
  );
}

export default DashboardPage;
