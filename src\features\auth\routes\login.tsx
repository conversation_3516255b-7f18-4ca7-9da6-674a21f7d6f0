import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import logo from "../../../assets/images/logo.png";

// Validation schema using Zod
const loginSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

type LoginFormInputs = z.infer<typeof loginSchema>;

const AGKraftLogin = () => {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<LoginFormInputs>({
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = async (data: LoginFormInputs) => {
    await new Promise((resolve) => setTimeout(resolve, 1000));
    console.log("Login data:", data);
  };

  return (
    <div className="flex justify-center items-center">
      <div className="absolute top-5 left-5 hidden lg:block">
        <img src={logo} className="h-[80px]" alt="Logo" />
      </div>

      {/* Login card */}
      <main className="flex shadow-transparent justify-center items-center z-10 py-10 bg-white rounded-lg">
        <div className=" dark:bg-[#0f0f10] rounded-2xl shadow-xl p-8 sm:p-10 w-full">
          {/* Top logo for small screens */}
          <div className="flex items-center gap-4 mb-6 lg:hidden">
            <div className="w-12 h-12 rounded-md bg-gradient-to-br from-[#F44336] to-[#FF9800] flex items-center justify-center">
              <img src={logo} className="w-8 h-8 contain-content" alt="Logo" />
            </div>
            <div>
              <h1 className="text-2xl font-semibold text-[#121212]">
                Welcome back
              </h1>
              <p className="text-sm text-gray-500">Admin sign in</p>
            </div>
          </div>

          <h1 className="hidden lg:block text-3xl font-bold text-[#121212] mb-1">
            Sign in to AGKRAFT
          </h1>
          <p className="hidden lg:block text-sm text-gray-600 mb-6">
            Use your admin credentials to access the dashboard
          </p>

          {/* Form */}
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            {/* Email */}
            <div>
              <div className="flex items-center">
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-gray-700"
                >
                  Email address
                </label>
              </div>

              <input
                {...register("email")}
                id="email"
                type="email"
                placeholder="<EMAIL>"
                className={`mt-1 block w-full rounded-xl border bg-white px-4 py-3 text-sm shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#F44336] ${
                  errors.email ? "border-red-500" : "border-gray-200"
                }`}
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.email.message}
                </p>
              )}
            </div>

            {/* Password */}
            <div>
              <div className="flex items-center justify-between">
                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-gray-700"
                >
                  Password
                </label>
              </div>
              <input
                {...register("password")}
                id="password"
                type="password"
                placeholder="Enter your password"
                className={`mt-1 block w-full rounded-xl border bg-white px-4 py-3 text-sm shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#F44336] ${
                  errors.password ? "border-red-500" : "border-gray-200"
                }`}
              />
              {errors.password && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.password.message}
                </p>
              )}
            </div>

            {/* Remember me */}
            <div className="flex items-center justify-between">
              <label className="inline-flex items-center gap-2 text-sm">
                <input
                  type="checkbox"
                  className="h-4 w-4 rounded border-gray-300 text-[#F44336] focus:ring-[#F44336]"
                />
                <span className="text-gray-600">Remember me</span>
              </label>
              <a href="#" className="text-sm text-gray-600 hover:underline">
                Need help?
              </a>
            </div>

            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full rounded-xl px-4 py-3 text-white font-semibold shadow-md bg-gradient-to-r from-[#F44336] to-[#FF9800] hover:scale-[1.01] transform transition disabled:opacity-70"
            >
              {isSubmitting ? "Signing in..." : "Sign in"}
            </button>
          </form>

          <div className="mt-6 text-center text-xs text-gray-400">
            By signing in you agree to AGKRAFT's{" "}
          </div>
        </div>
      </main>
    </div>
  );
};

export default AGKraftLogin;
