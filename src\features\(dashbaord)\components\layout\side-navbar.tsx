import { NavLink } from "react-router-dom";
import { sidebarModules } from "../../../../data/sidebar-modules";

interface SideNavbarProps {
  navbar: boolean;
}

const SideNavbar: React.FC<SideNavbarProps> = ({ navbar }) => {
  return (
    <div
      className={`h-[calc(100vh-70px)] bg-white border-r-2 transition-all duration-300 ${
        navbar ? "w-64" : "w-16"
      }`}
    >
      <div className="p-4">
        <nav className="space-y-2">
          {sidebarModules.map((module) => {
            const IconComponent = module.icon;
            return (
              <NavLink
                key={module.id}
                to={module.url}
                className={({ isActive }) =>
                  `flex items-center gap-3 p-3 rounded-lg transition-colors duration-200 ${
                    isActive
                      ? "bg-[#68473B] text-white"
                      : "text-[#68473B] hover:bg-gray-100"
                  }`
                }
              >
                <IconComponent className="text-xl flex-shrink-0" />
                {navbar && (
                  <span className="font-medium whitespace-nowrap">
                    {module.name}
                  </span>
                )}
              </NavLink>
            );
          })}
        </nav>
      </div>
    </div>
  );
};

export default SideNavbar;