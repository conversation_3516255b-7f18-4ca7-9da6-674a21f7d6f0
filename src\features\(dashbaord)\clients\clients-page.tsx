const ClientsPage = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-[#68473B]">Clients</h1>
        <button className="bg-[#68473B] text-white px-4 py-2 rounded-lg hover:bg-[#5a3d32] transition-colors">
          Add New Client
        </button>
      </div>
      
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4 font-semibold text-[#68473B]">Name</th>
                  <th className="text-left py-3 px-4 font-semibold text-[#68473B]">Email</th>
                  <th className="text-left py-3 px-4 font-semibold text-[#68473B]">Phone</th>
                  <th className="text-left py-3 px-4 font-semibold text-[#68473B]">Status</th>
                  <th className="text-left py-3 px-4 font-semibold text-[#68473B]">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-b hover:bg-gray-50">
                  <td className="py-3 px-4">John Doe</td>
                  <td className="py-3 px-4"><EMAIL></td>
                  <td className="py-3 px-4">+91 9876543210</td>
                  <td className="py-3 px-4">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm">
                      Active
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <button className="text-[#68473B] hover:underline mr-3">Edit</button>
                    <button className="text-red-600 hover:underline">Delete</button>
                  </td>
                </tr>
                <tr className="border-b hover:bg-gray-50">
                  <td className="py-3 px-4">Jane Smith</td>
                  <td className="py-3 px-4"><EMAIL></td>
                  <td className="py-3 px-4">+91 9876543211</td>
                  <td className="py-3 px-4">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm">
                      Active
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <button className="text-[#68473B] hover:underline mr-3">Edit</button>
                    <button className="text-red-600 hover:underline">Delete</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClientsPage;
