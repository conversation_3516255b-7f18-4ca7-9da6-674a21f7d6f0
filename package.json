{"name": "lifeplanner", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.2.2", "@mui/icons-material": "^7.3.4", "@tailwindcss/vite": "^4.1.14", "@tanstack/react-query": "^5.90.2", "@tanstack/react-query-devtools": "^5.90.2", "react": "^19.1.1", "react-dom": "^19.1.1", "react-icons": "^4.12.0", "react-router-dom": "^7.9.3", "zod": "^4.1.11"}, "devDependencies": {"@eslint/js": "^9.36.0", "@types/node": "^24.6.0", "@types/react": "^19.1.16", "@types/react-dom": "^19.1.9", "@vitejs/plugin-react": "^5.0.4", "autoprefixer": "^10.4.21", "eslint": "^9.36.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.22", "globals": "^16.4.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.14", "typescript": "~5.9.3", "typescript-eslint": "^8.45.0", "vite": "^7.1.7"}}