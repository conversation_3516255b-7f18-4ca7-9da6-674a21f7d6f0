const PaymentsPage = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-[#68473B]">Payments</h1>
        <button className="bg-[#68473B] text-white px-4 py-2 rounded-lg hover:bg-[#5a3d32] transition-colors">
          Record Payment
        </button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-sm font-medium text-gray-500">Total Received</h3>
          <p className="text-2xl font-bold text-green-600 mt-2">₹2,05,000</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-sm font-medium text-gray-500">Pending</h3>
          <p className="text-2xl font-bold text-orange-600 mt-2">₹45,000</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-sm font-medium text-gray-500">Overdue</h3>
          <p className="text-2xl font-bold text-red-600 mt-2">₹15,000</p>
        </div>
      </div>
      
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6">
          <h2 className="text-lg font-semibold text-[#68473B] mb-4">Recent Payments</h2>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4 font-semibold text-[#68473B]">Invoice</th>
                  <th className="text-left py-3 px-4 font-semibold text-[#68473B]">Client</th>
                  <th className="text-left py-3 px-4 font-semibold text-[#68473B]">Amount</th>
                  <th className="text-left py-3 px-4 font-semibold text-[#68473B]">Date</th>
                  <th className="text-left py-3 px-4 font-semibold text-[#68473B]">Status</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-b hover:bg-gray-50">
                  <td className="py-3 px-4">#INV-001</td>
                  <td className="py-3 px-4">John Doe</td>
                  <td className="py-3 px-4">₹25,000</td>
                  <td className="py-3 px-4">2024-01-15</td>
                  <td className="py-3 px-4">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm">
                      Paid
                    </span>
                  </td>
                </tr>
                <tr className="border-b hover:bg-gray-50">
                  <td className="py-3 px-4">#INV-002</td>
                  <td className="py-3 px-4">Jane Smith</td>
                  <td className="py-3 px-4">₹15,000</td>
                  <td className="py-3 px-4">2024-01-10</td>
                  <td className="py-3 px-4">
                    <span className="bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-sm">
                      Pending
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentsPage;
