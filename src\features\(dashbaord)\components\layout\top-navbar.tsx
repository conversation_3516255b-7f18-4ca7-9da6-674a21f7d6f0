import NotificationsIcon from "@mui/icons-material/Notifications";
import { FaBars } from "react-icons/fa6";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import { IoMdSettings } from "react-icons/io";
import logo from "../../../../assets/images/logo.png";

interface TopNavbarProps {
  handleNavbar: () => void;
  navbar: boolean;
}

const TopNavbar: React.FC<TopNavbarProps> = ({ handleNavbar }) => {
  return (
    <div className="border-b-2 w-full bg-white p-2">
      <div className="flex items-center justify-between px-5">
        <div className="flex items-center">
          <div
            onClick={handleNavbar}
            className="text-[20px] flex justify-center text-[#68473B] cursor-pointer"
          >
            <FaBars />
          </div>
          <div className="w-[250px] flex justify-center">
            <img className="h-[50px]" src={logo} alt="Logo" />
          </div>
        </div>

        <div className="flex justify-center text-[#68473B] gap-3 items-center relative">
          <IoMdSettings className="text-lg cursor-pointer" />
          <NotificationsIcon className="cursor-pointer" />

          {/* User Profile Dropdown */}
          <div className="flex flex-col gap-2">
            <div className="flex gap-2 items-center">
              <div className="flex cursor-pointer">
                <p className="text-[#68473B] font-semibold">kunu</p>
                <ArrowDropDownIcon />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TopNavbar;
